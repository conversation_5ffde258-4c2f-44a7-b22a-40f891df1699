{% extends "base.html" %}

{% block title %}浏览器多账号绿色版 - 实例管理{% endblock %}

{% block content %}
<!-- 统计信息卡片 -->
<div class="stats-grid">
    <div class="card stats-card">
        <div class="card-body">
            <div class="stats-content">
                <div class="stats-info">
                    <h3>{{ instances|length }}</h3>
                    <p>总实例数</p>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-browser"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card stats-card success">
        <div class="card-body">
            <div class="stats-content">
                <div class="stats-info">
                    <h3 id="runningCount">0</h3>
                    <p>运行中</p>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-play-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card stats-card secondary">
        <div class="card-body">
            <div class="stats-content">
                <div class="stats-info">
                    <h3 id="stoppedCount">{{ instances|length }}</h3>
                    <p>已停止</p>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-stop-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="card stats-card info">
        <div class="card-body">
            <div class="stats-content">
                <div class="stats-info">
                    <h3 id="chromeStatus">
                        <i class="fas fa-check"></i>
                    </h3>
                    <p>Chrome可用</p>
                </div>
                <div class="stats-icon">
                    <i class="fab fa-chrome"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索实例名称或描述..."
                           onkeyup="filterInstances()" style="border-radius: 0 8px 8px 0;">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-control" id="statusFilter" onchange="filterInstances()"
                        style="border-radius: 8px;">
                    <option value="">所有状态</option>
                    <option value="running">运行中</option>
                    <option value="stopped">已停止</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-control" id="groupFilter" onchange="filterInstances()"
                        style="border-radius: 8px;">
                    <option value="">所有分组</option>
                    <option value="default">默认分组</option>
                    <option value="work">工作分组</option>
                    <option value="personal">个人分组</option>
                    <option value="dev">开发分组</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作卡片 -->
<div class="card" style="margin-bottom: 2rem;">
    <div class="card-header">
        <i class="fas fa-tools me-2"></i>
        快速操作
    </div>
    <div class="card-body">
        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
            <button type="button" class="btn btn-primary" onclick="showCreateModal()">
                <i class="fas fa-plus"></i>
                新建实例
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="refreshInstances()">
                <i class="fas fa-sync-alt"></i>
                刷新列表
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="showBatchModal()">
                <i class="fas fa-layer-group"></i>
                批量配置
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="checkHealth()">
                <i class="fas fa-heartbeat"></i>
                系统检查
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="toggleBatchMode()">
                <i class="fas fa-check-square"></i>
                批量操作
            </button>
            <button type="button" class="btn btn-outline-info" onclick="exportConfig()">
                <i class="fas fa-download"></i>
                导出配置
            </button>
            <button type="button" class="btn btn-outline-success" onclick="showImportModal()">
                <i class="fas fa-upload"></i>
                导入配置
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="showHelpModal()">
                <i class="fas fa-question-circle"></i>
                帮助
            </button>
            <button type="button" class="btn btn-outline-warning" onclick="quickLaunchAll()">
                <i class="fas fa-rocket"></i>
                启动全部
            </button>
        </div>
    </div>
</div>

<!-- 批量操作工具栏 -->
<div class="card" id="batchToolbar" style="display: none; margin-bottom: 1rem;">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <span class="text-muted">已选择 <span id="selectedCount">0</span> 个实例</span>
            </div>
            <div>
                <button type="button" class="btn btn-success btn-sm" onclick="batchLaunch()">
                    <i class="fas fa-play"></i> 批量启动
                </button>
                <button type="button" class="btn btn-danger btn-sm" onclick="batchDelete()">
                    <i class="fas fa-trash"></i> 批量删除
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectAll()">
                    <i class="fas fa-check-double"></i> 全选
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                    <i class="fas fa-times"></i> 清除选择
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 实例列表 -->
<div class="card">
    <div class="card-header">
        <i class="fas fa-th-large me-2"></i>
        浏览器实例
        <span class="badge bg-primary ms-2">{{ instances|length }}</span>
    </div>
    <div class="card-body">
        {% if instances %}
        <div class="instances-grid" id="instancesList">
            {% for instance in instances %}
            <div class="card instance-card" data-instance="{{ instance.name }}">
                <div class="card-body">
                    <div class="instance-header">
                        <div>
                            <div class="instance-title">
                                <input type="checkbox" class="form-check-input me-2 instance-checkbox"
                                       value="{{ instance.name }}" style="display: none;">
                                <i class="fab fa-chrome me-2" style="color: var(--primary-color);"></i>
                                {{ instance.display_name or instance.name }}
                            </div>
                            {% if instance.description %}
                            <div class="instance-description">
                                {{ instance.description }}
                            </div>
                            {% endif %}
                        </div>
                        <span class="status-badge status-stopped" id="status-{{ instance.name }}">
                            已停止
                        </span>
                    </div>

                    <div class="instance-meta">
                        <div class="meta-item">
                            <i class="fas fa-home"></i>
                            {{ instance.homepage or 'https://www.google.com' }}
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-folder"></i>
                            {{ instance.group or 'default' }}
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-calendar"></i>
                            {{ instance.created_date or '未知' }}
                        </div>
                    </div>

                    <div class="instance-actions">
                        <button type="button" class="btn btn-success"
                                onclick="toggleInstance('{{ instance.name }}')"
                                id="toggle-{{ instance.name }}">
                            <i class="fas fa-play"></i>
                            <span class="action-text">启动</span>
                            <span class="loading spinner-border spinner-border-sm"></span>
                        </button>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary"
                                    onclick="editInstance('{{ instance.name }}')">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                            <button type="button" class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split"
                                    data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="visually-hidden">更多操作</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="viewInstanceDetails('{{ instance.name }}')">
                                    <i class="fas fa-info-circle me-2"></i>查看详情
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="duplicateInstance('{{ instance.name }}')">
                                    <i class="fas fa-copy me-2"></i>复制实例
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="openInstanceFolder('{{ instance.name }}')">
                                    <i class="fas fa-folder-open me-2"></i>打开目录
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="showInstanceSettings('{{ instance.name }}')">
                                    <i class="fas fa-cog me-2"></i>高级设置
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteInstance('{{ instance.name }}')">
                                    <i class="fas fa-trash me-2"></i>删除实例
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <i class="fas fa-browser"></i>
            <h4>暂无浏览器实例</h4>
            <p>点击"新建实例"创建您的第一个浏览器实例</p>
            <button type="button" class="btn btn-primary" onclick="showCreateModal()">
                <i class="fas fa-plus me-2"></i>立即创建
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- 新建实例模态框 -->
<div class="modal fade" id="createModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 12px; border: none;">
            <div class="modal-header" style="background: var(--primary-color); color: white; border-radius: 12px 12px 0 0;">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>新建浏览器实例
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <form id="createForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="instanceName" class="form-label" style="font-weight: 600; color: var(--text-primary);">实例名称 *</label>
                            <input type="text" class="form-control" id="instanceName" required
                                   placeholder="例如：work, personal, shopping"
                                   style="border-radius: 8px; border: 1px solid var(--border-color); padding: 0.75rem;">
                            <div class="form-text" style="color: var(--text-secondary);">只能包含字母、数字、下划线和连字符</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="displayName" class="form-label" style="font-weight: 600; color: var(--text-primary);">显示名称 *</label>
                            <input type="text" class="form-control" id="displayName" required
                                   placeholder="例如：Chrome - 工作"
                                   style="border-radius: 8px; border: 1px solid var(--border-color); padding: 0.75rem;">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label" style="font-weight: 600; color: var(--text-primary);">描述</label>
                        <textarea class="form-control" id="description" rows="3"
                                  placeholder="简要描述这个实例的用途"
                                  style="border-radius: 8px; border: 1px solid var(--border-color); padding: 0.75rem;"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="homepage" class="form-label" style="font-weight: 600; color: var(--text-primary);">主页URL</label>
                            <input type="url" class="form-control" id="homepage"
                                   value="https://www.google.com"
                                   placeholder="https://www.google.com"
                                   style="border-radius: 8px; border: 1px solid var(--border-color); padding: 0.75rem;">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="instanceGroup" class="form-label" style="font-weight: 600; color: var(--text-primary);">分组</label>
                            <select class="form-control" id="instanceGroup"
                                    style="border-radius: 8px; border: 1px solid var(--border-color); padding: 0.75rem;">
                                <option value="default">默认分组</option>
                                <option value="work">工作分组</option>
                                <option value="personal">个人分组</option>
                                <option value="dev">开发分组</option>
                                <option value="shopping">购物分组</option>
                                <option value="gaming">游戏分组</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="padding: 1.5rem 2rem; border-top: 1px solid var(--border-color);">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border-radius: 8px;">取消</button>
                <button type="button" class="btn btn-primary" onclick="createInstance()" style="border-radius: 8px;">
                    <i class="fas fa-plus me-2"></i>创建实例
                    <span class="loading spinner-border spinner-border-sm ms-2"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑实例模态框 -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 12px; border: none;">
            <div class="modal-header" style="background: var(--info-color); color: white; border-radius: 12px 12px 0 0;">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>编辑浏览器实例
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <form id="editForm">
                    <input type="hidden" id="editInstanceName">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editDisplayName" class="form-label" style="font-weight: 600; color: var(--text-primary);">显示名称 *</label>
                            <input type="text" class="form-control" id="editDisplayName" required
                                   style="border-radius: 8px; border: 1px solid var(--border-color); padding: 0.75rem;">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="editIcon" class="form-label" style="font-weight: 600; color: var(--text-primary);">图标</label>
                            <select class="form-control" id="editIcon"
                                    style="border-radius: 8px; border: 1px solid var(--border-color); padding: 0.75rem;">
                                <option value="default">默认图标</option>
                                <option value="work">工作</option>
                                <option value="personal">个人</option>
                                <option value="shopping">购物</option>
                                <option value="dev">开发</option>
                                <option value="gaming">游戏</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editDescription" class="form-label" style="font-weight: 600; color: var(--text-primary);">描述</label>
                        <textarea class="form-control" id="editDescription" rows="3"
                                  style="border-radius: 8px; border: 1px solid var(--border-color); padding: 0.75rem;"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="editHomepage" class="form-label" style="font-weight: 600; color: var(--text-primary);">主页URL</label>
                        <input type="url" class="form-control" id="editHomepage"
                               style="border-radius: 8px; border: 1px solid var(--border-color); padding: 0.75rem;">
                    </div>

                    <div class="mb-3">
                        <label class="form-label" style="font-weight: 600; color: var(--text-primary);">Chrome启动参数</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editArgPerformance">
                            <label class="form-check-label" for="editArgPerformance">
                                性能优化模式
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editArgPrivacy">
                            <label class="form-check-label" for="editArgPrivacy">
                                隐私保护模式
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editArgDev">
                            <label class="form-check-label" for="editArgDev">
                                开发者模式
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="padding: 1.5rem 2rem; border-top: 1px solid var(--border-color);">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border-radius: 8px;">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateInstance()" style="border-radius: 8px; background: var(--info-color); border-color: var(--info-color);">
                    <i class="fas fa-save me-2"></i>保存更改
                    <span class="loading spinner-border spinner-border-sm ms-2"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量配置模态框 -->
<div class="modal fade" id="batchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 12px; border: none;">
            <div class="modal-header" style="background: var(--warning-color); color: white; border-radius: 12px 12px 0 0;">
                <h5 class="modal-title">
                    <i class="fas fa-layer-group me-2"></i>批量配置
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <p style="color: var(--text-secondary); margin-bottom: 1.5rem;">选择预定义的配置模板，一键创建多个浏览器实例。</p>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card" style="border: 1px solid var(--border-color); border-radius: 8px;">
                            <div class="card-body">
                                <h6 class="card-title" style="color: var(--text-primary);">基础实例集合</h6>
                                <p class="card-text small" style="color: var(--text-secondary);">工作、个人、购物三个基础实例</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="applyBatchConfig('basic_set')" style="border-radius: 6px;">
                                    <i class="fas fa-download me-1"></i>应用配置
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <div class="card" style="border: 1px solid var(--border-color); border-radius: 8px;">
                            <div class="card-body">
                                <h6 class="card-title" style="color: var(--text-primary);">开发者实例集合</h6>
                                <p class="card-text small" style="color: var(--text-secondary);">专为开发者设计的实例集合</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="applyBatchConfig('developer_set')" style="border-radius: 6px;">
                                    <i class="fas fa-download me-1"></i>应用配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <hr style="margin: 1.5rem 0; border-color: var(--border-color);">

                <div class="mb-3">
                    <label class="form-label" style="font-weight: 600; color: var(--text-primary);">自定义批量创建</label>
                    <div class="row">
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="batchPrefix" placeholder="实例前缀"
                                   style="border-radius: 8px; border: 1px solid var(--border-color);">
                        </div>
                        <div class="col-md-4">
                            <input type="number" class="form-control" id="batchCount" placeholder="数量" min="1" max="10" value="3"
                                   style="border-radius: 8px; border: 1px solid var(--border-color);">
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-primary w-100" onclick="createBatchInstances()" style="border-radius: 8px;">
                                <i class="fas fa-magic me-1"></i>批量创建
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导入配置模态框 -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 12px; border: none;">
            <div class="modal-header" style="background: var(--success-color); color: white; border-radius: 12px 12px 0 0;">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>导入配置
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <p style="color: var(--text-secondary); margin-bottom: 1.5rem;">
                    选择之前导出的配置文件，系统将自动创建其中的实例配置。
                </p>

                <div class="mb-3">
                    <label for="importFile" class="form-label" style="font-weight: 600; color: var(--text-primary);">选择配置文件</label>
                    <input type="file" class="form-control" id="importFile" accept=".json"
                           style="border-radius: 8px; border: 1px solid var(--border-color); padding: 0.75rem;">
                    <div class="form-text" style="color: var(--text-secondary);">支持JSON格式的配置文件</div>
                </div>

                <div class="mb-3">
                    <label class="form-label" style="font-weight: 600; color: var(--text-primary);">导入选项</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="skipExisting" checked>
                        <label class="form-check-label" for="skipExisting">
                            跳过已存在的实例
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="createBackup">
                        <label class="form-check-label" for="createBackup">
                            导入前创建当前配置备份
                        </label>
                    </div>
                </div>

                <div id="importPreview" style="display: none;">
                    <h6 style="color: var(--text-primary); margin-bottom: 1rem;">配置预览</h6>
                    <div class="border rounded p-3" style="background-color: var(--main-bg); max-height: 200px; overflow-y: auto;">
                        <div id="previewContent"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="padding: 1.5rem 2rem; border-top: 1px solid var(--border-color);">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" style="border-radius: 8px;">取消</button>
                <button type="button" class="btn btn-success" onclick="importConfig()" style="border-radius: 8px;">
                    <i class="fas fa-upload me-2"></i>开始导入
                    <span class="loading spinner-border spinner-border-sm ms-2"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 帮助模态框 -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 12px; border: none;">
            <div class="modal-header" style="background: var(--secondary-color); color: white; border-radius: 12px 12px 0 0;">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle me-2"></i>使用帮助
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" style="padding: 2rem;">
                <div class="row">
                    <div class="col-md-6">
                        <h6 style="color: var(--text-primary); margin-bottom: 1rem;">
                            <i class="fas fa-keyboard me-2"></i>键盘快捷键
                        </h6>
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td><kbd>Ctrl + N</kbd></td>
                                    <td>新建实例</td>
                                </tr>
                                <tr>
                                    <td><kbd>Ctrl + R</kbd></td>
                                    <td>刷新列表</td>
                                </tr>
                                <tr>
                                    <td><kbd>Ctrl + B</kbd></td>
                                    <td>批量操作</td>
                                </tr>
                                <tr>
                                    <td><kbd>Ctrl + E</kbd></td>
                                    <td>导出配置</td>
                                </tr>
                                <tr>
                                    <td><kbd>Ctrl + I</kbd></td>
                                    <td>导入配置</td>
                                </tr>
                                <tr>
                                    <td><kbd>/</kbd></td>
                                    <td>聚焦搜索框</td>
                                </tr>
                                <tr>
                                    <td><kbd>Esc</kbd></td>
                                    <td>清空搜索</td>
                                </tr>
                                <tr>
                                    <td><kbd>F5</kbd></td>
                                    <td>刷新页面</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 style="color: var(--text-primary); margin-bottom: 1rem;">
                            <i class="fas fa-lightbulb me-2"></i>使用技巧
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                实例状态每5秒自动更新
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                支持按名称、描述、分组过滤
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                批量操作可同时管理多个实例
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                配置导入导出便于备份迁移
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                每个实例数据完全隔离
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                支持自定义Chrome启动参数
                            </li>
                        </ul>

                        <h6 style="color: var(--text-primary); margin-bottom: 1rem; margin-top: 2rem;">
                            <i class="fas fa-info-circle me-2"></i>系统信息
                        </h6>
                        <p class="small text-muted">
                            <strong>版本：</strong>v0.002<br>
                            <strong>框架：</strong>Flask + Bootstrap 5<br>
                            <strong>浏览器：</strong>Chrome Portable<br>
                            <strong>开发：</strong>AI Architecture System
                        </p>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="padding: 1.5rem 2rem; border-top: 1px solid var(--border-color);">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="border-radius: 8px;">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 显示新建实例模态框
function showCreateModal() {
    const modal = new bootstrap.Modal(document.getElementById('createModal'));
    modal.show();
}

// 显示批量配置模态框
function showBatchModal() {
    const modal = new bootstrap.Modal(document.getElementById('batchModal'));
    modal.show();
}

// 刷新实例列表
async function refreshInstances() {
    try {
        showToast('正在刷新实例列表...', 'info');

        // 重新加载页面来刷新实例列表
        location.reload();

    } catch (error) {
        showToast(`刷新失败: ${error.message}`, 'error');
    }
}

// 创建实例
async function createInstance() {
    const button = event.target;
    showLoading(button);
    
    try {
        const formData = {
            name: document.getElementById('instanceName').value.trim(),
            display_name: document.getElementById('displayName').value.trim(),
            description: document.getElementById('description').value.trim(),
            homepage: document.getElementById('homepage').value.trim(),
            group: document.getElementById('instanceGroup').value
        };
        
        const result = await apiRequest('/api/instances', {
            method: 'POST',
            body: JSON.stringify(formData)
        });
        
        showToast('实例创建成功！', 'success');
        bootstrap.Modal.getInstance(document.getElementById('createModal')).hide();
        
        // 重置表单
        document.getElementById('createForm').reset();
        document.getElementById('homepage').value = 'https://www.google.com';
        
        // 刷新页面
        setTimeout(() => location.reload(), 1000);
        
    } catch (error) {
        showToast(error.message, 'error');
    } finally {
        hideLoading(button);
    }
}

// 启动实例
async function launchInstance(name) {
    const button = document.getElementById(`toggle-${name}`);
    showLoading(button);

    try {
        await apiRequest(`/api/instances/${name}/launch`, {
            method: 'POST'
        });

        showToast(`实例 ${name} 启动成功！`, 'success');

        // 更新状态显示
        updateInstanceUI(name, 'running');

    } catch (error) {
        showToast(error.message, 'error');
    } finally {
        hideLoading(button);
    }
}

// 停止实例
async function stopInstance(name) {
    const button = document.getElementById(`toggle-${name}`);
    showLoading(button);

    try {
        await apiRequest(`/api/instances/${name}/stop`, {
            method: 'POST'
        });

        showToast(`实例 ${name} 停止成功！`, 'success');

        // 更新状态显示
        updateInstanceUI(name, 'stopped');

    } catch (error) {
        showToast(`停止失败: ${error.message}`, 'error');
    } finally {
        hideLoading(button);
    }
}

// 切换实例状态（启动/停止）
async function toggleInstance(name) {
    const statusBadge = document.getElementById(`status-${name}`);
    const isRunning = statusBadge && statusBadge.classList.contains('status-running');

    if (isRunning) {
        await stopInstance(name);
    } else {
        await launchInstance(name);
    }
}

// 更新实例UI状态
function updateInstanceUI(name, status) {
    const statusBadge = document.getElementById(`status-${name}`);
    const toggleButton = document.getElementById(`toggle-${name}`);

    if (statusBadge) {
        if (status === 'running') {
            statusBadge.textContent = '运行中';
            statusBadge.className = 'status-badge status-running';
        } else {
            statusBadge.textContent = '已停止';
            statusBadge.className = 'status-badge status-stopped';
        }
    }

    if (toggleButton) {
        const icon = toggleButton.querySelector('i');
        const text = toggleButton.querySelector('.action-text');

        if (status === 'running') {
            toggleButton.className = 'btn btn-warning';
            if (icon) icon.className = 'fas fa-stop';
            if (text) text.textContent = '停止';
        } else {
            toggleButton.className = 'btn btn-success';
            if (icon) icon.className = 'fas fa-play';
            if (text) text.textContent = '启动';
        }
    }

    // 更新统计信息
    updateStats();
}

// 删除实例
async function deleteInstance(name) {
    if (!confirm(`确定要删除实例 "${name}" 吗？此操作不可恢复！`)) {
        return;
    }
    
    try {
        await apiRequest(`/api/instances/${name}`, {
            method: 'DELETE'
        });
        
        showToast(`实例 ${name} 删除成功！`, 'success');
        
        // 移除卡片
        const card = document.querySelector(`[data-instance="${name}"]`).closest('.col-lg-4');
        card.remove();
        
        // 更新统计
        updateStats();
        
    } catch (error) {
        showToast(error.message, 'error');
    }
}

// 编辑实例
async function editInstance(name) {
    try {
        // 获取实例详情
        const result = await apiRequest(`/api/instances/${name}`);
        const instance = result.data;

        // 填充表单
        document.getElementById('editInstanceName').value = instance.name;
        document.getElementById('editDisplayName').value = instance.display_name;
        document.getElementById('editDescription').value = instance.description || '';
        document.getElementById('editHomepage').value = instance.homepage || '';
        document.getElementById('editIcon').value = instance.icon || 'default';

        // 设置Chrome参数复选框
        const chromeArgs = instance.chrome_args || [];
        document.getElementById('editArgPerformance').checked = chromeArgs.includes('--max_old_space_size=4096');
        document.getElementById('editArgPrivacy').checked = chromeArgs.includes('--incognito');
        document.getElementById('editArgDev').checked = chromeArgs.includes('--disable-web-security');

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('editModal'));
        modal.show();

    } catch (error) {
        showToast(`获取实例信息失败: ${error.message}`, 'error');
    }
}

// 更新实例
async function updateInstance() {
    const button = event.target;
    showLoading(button);

    try {
        const name = document.getElementById('editInstanceName').value;

        // 构建Chrome参数
        const chromeArgs = [
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-background-timer-throttling'
        ];

        if (document.getElementById('editArgPerformance').checked) {
            chromeArgs.push('--disable-backgrounding-occluded-windows');
            chromeArgs.push('--disable-renderer-backgrounding');
            chromeArgs.push('--max_old_space_size=4096');
        }

        if (document.getElementById('editArgPrivacy').checked) {
            chromeArgs.push('--incognito');
            chromeArgs.push('--disable-sync');
            chromeArgs.push('--disable-background-networking');
        }

        if (document.getElementById('editArgDev').checked) {
            chromeArgs.push('--disable-web-security');
            chromeArgs.push('--disable-features=VizDisplayCompositor');
            chromeArgs.push('--allow-running-insecure-content');
        }

        const formData = {
            display_name: document.getElementById('editDisplayName').value.trim(),
            description: document.getElementById('editDescription').value.trim(),
            homepage: document.getElementById('editHomepage').value.trim(),
            icon: document.getElementById('editIcon').value,
            chrome_args: chromeArgs
        };

        const result = await apiRequest(`/api/instances/${name}`, {
            method: 'PUT',
            body: JSON.stringify(formData)
        });

        showToast('实例更新成功！', 'success');
        bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();

        // 刷新页面
        setTimeout(() => location.reload(), 1000);

    } catch (error) {
        showToast(error.message, 'error');
    } finally {
        hideLoading(button);
    }
}

// 应用批量配置
async function applyBatchConfig(template) {
    try {
        const result = await apiRequest('/api/batch/create', {
            method: 'POST',
            body: JSON.stringify({ template: template })
        });

        showToast(result.message, 'success');
        bootstrap.Modal.getInstance(document.getElementById('batchModal')).hide();

        // 刷新页面
        setTimeout(() => location.reload(), 1500);

    } catch (error) {
        showToast(`批量配置失败: ${error.message}`, 'error');
    }
}

// 批量创建实例
async function createBatchInstances() {
    const prefix = document.getElementById('batchPrefix').value.trim();
    const count = parseInt(document.getElementById('batchCount').value);

    if (!prefix) {
        showToast('请输入实例前缀', 'warning');
        return;
    }

    if (!count || count < 1 || count > 10) {
        showToast('实例数量必须在1-10之间', 'warning');
        return;
    }

    try {
        const result = await apiRequest('/api/batch/create', {
            method: 'POST',
            body: JSON.stringify({
                config: {
                    prefix: prefix,
                    count: count
                }
            })
        });

        showToast(result.message, 'success');
        bootstrap.Modal.getInstance(document.getElementById('batchModal')).hide();

        // 重置表单
        document.getElementById('batchPrefix').value = '';
        document.getElementById('batchCount').value = '3';

        // 刷新页面
        setTimeout(() => location.reload(), 1500);

    } catch (error) {
        showToast(`批量创建失败: ${error.message}`, 'error');
    }
}

// 检查系统健康状态
async function checkHealth() {
    try {
        const result = await apiRequest('/health');
        const status = result.chrome_portable ? '正常' : '异常';
        const type = result.chrome_portable ? 'success' : 'warning';
        showToast(`系统状态: ${status}`, type);
    } catch (error) {
        showToast('健康检查失败', 'error');
    }
}

// 更新统计信息
function updateStats() {
    const totalInstances = document.querySelectorAll('.instance-card').length;
    const runningInstances = document.querySelectorAll('.status-running').length;

    // 更新总实例数
    const totalElements = document.querySelectorAll('.stats-info h3');
    if (totalElements.length > 0) {
        totalElements[0].textContent = totalInstances;
    }

    // 更新运行中实例数
    document.getElementById('runningCount').textContent = runningInstances;

    // 更新已停止实例数
    document.getElementById('stoppedCount').textContent = totalInstances - runningInstances;
}

// 过滤实例
function filterInstances() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const groupFilter = document.getElementById('groupFilter').value;

    const instanceCards = document.querySelectorAll('.instance-card');
    let visibleCount = 0;

    instanceCards.forEach(card => {
        const instanceName = card.querySelector('.instance-title').textContent.toLowerCase();
        const instanceDescription = card.querySelector('.instance-description')?.textContent.toLowerCase() || '';
        const instanceStatus = card.querySelector('.status-badge').classList.contains('status-running') ? 'running' : 'stopped';

        // 获取分组信息
        const groupElement = card.querySelector('.meta-item:nth-child(2)');
        const instanceGroup = groupElement ? groupElement.textContent.trim() : 'default';

        // 搜索匹配
        const matchesSearch = !searchTerm ||
            instanceName.includes(searchTerm) ||
            instanceDescription.includes(searchTerm);

        // 状态匹配
        const matchesStatus = !statusFilter || instanceStatus === statusFilter;

        // 分组匹配
        const matchesGroup = !groupFilter || instanceGroup === groupFilter;

        if (matchesSearch && matchesStatus && matchesGroup) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    // 显示/隐藏空状态
    const emptyState = document.querySelector('.empty-state');
    const instancesList = document.getElementById('instancesList');

    if (visibleCount === 0 && instanceCards.length > 0) {
        if (!document.getElementById('noResultsMessage')) {
            const noResults = document.createElement('div');
            noResults.id = 'noResultsMessage';
            noResults.className = 'empty-state';
            noResults.innerHTML = `
                <i class="fas fa-search"></i>
                <h4>未找到匹配的实例</h4>
                <p>尝试调整搜索条件或过滤器</p>
                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                    <i class="fas fa-times me-2"></i>清除过滤器
                </button>
            `;
            instancesList.appendChild(noResults);
        }
        document.getElementById('noResultsMessage').style.display = 'block';
    } else {
        const noResultsMessage = document.getElementById('noResultsMessage');
        if (noResultsMessage) {
            noResultsMessage.style.display = 'none';
        }
    }
}

// 清除过滤器
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('groupFilter').value = '';
    filterInstances();
}

// 批量操作相关变量
let batchMode = false;
let selectedInstances = new Set();

// 切换批量操作模式
function toggleBatchMode() {
    batchMode = !batchMode;
    const checkboxes = document.querySelectorAll('.instance-checkbox');
    const toolbar = document.getElementById('batchToolbar');
    const button = event.target;

    if (batchMode) {
        checkboxes.forEach(cb => cb.style.display = 'inline-block');
        toolbar.style.display = 'block';
        button.innerHTML = '<i class="fas fa-times"></i> 退出批量';
        button.className = 'btn btn-outline-danger';
    } else {
        checkboxes.forEach(cb => {
            cb.style.display = 'none';
            cb.checked = false;
        });
        toolbar.style.display = 'none';
        button.innerHTML = '<i class="fas fa-check-square"></i> 批量操作';
        button.className = 'btn btn-outline-warning';
        selectedInstances.clear();
        updateSelectedCount();
    }
}

// 更新选中数量
function updateSelectedCount() {
    document.getElementById('selectedCount').textContent = selectedInstances.size;
}

// 全选
function selectAll() {
    const checkboxes = document.querySelectorAll('.instance-checkbox');
    checkboxes.forEach(cb => {
        cb.checked = true;
        selectedInstances.add(cb.value);
    });
    updateSelectedCount();
}

// 清除选择
function clearSelection() {
    const checkboxes = document.querySelectorAll('.instance-checkbox');
    checkboxes.forEach(cb => {
        cb.checked = false;
    });
    selectedInstances.clear();
    updateSelectedCount();
}

// 批量启动
async function batchLaunch() {
    if (selectedInstances.size === 0) {
        showToast('请先选择要启动的实例', 'warning');
        return;
    }

    try {
        const result = await apiRequest('/api/batch/launch', {
            method: 'POST',
            body: JSON.stringify({ instances: Array.from(selectedInstances) })
        });

        showToast(result.message, 'success');
        setTimeout(() => location.reload(), 1000);

    } catch (error) {
        showToast(error.message, 'error');
    }
}

// 批量删除
async function batchDelete() {
    if (selectedInstances.size === 0) {
        showToast('请先选择要删除的实例', 'warning');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedInstances.size} 个实例吗？此操作不可恢复！`)) {
        return;
    }

    try {
        const result = await apiRequest('/api/batch/delete', {
            method: 'POST',
            body: JSON.stringify({ instances: Array.from(selectedInstances) })
        });

        showToast(result.message, 'success');
        setTimeout(() => location.reload(), 1000);

    } catch (error) {
        showToast(error.message, 'error');
    }
}

// 实时更新实例状态
async function updateInstancesStatus() {
    try {
        const result = await apiRequest('/api/instances/status/all');
        const statusList = result.data;

        statusList.forEach(item => {
            const statusBadge = document.getElementById(`status-${item.name}`);
            if (statusBadge) {
                if (item.status === 'running') {
                    statusBadge.textContent = '运行中';
                    statusBadge.className = 'status-badge status-running';
                } else {
                    statusBadge.textContent = '已停止';
                    statusBadge.className = 'status-badge status-stopped';
                }
            }
        });

        updateStats();
    } catch (error) {
        console.error('更新状态失败:', error);
    }
}

// 导出配置
async function exportConfig() {
    try {
        const result = await apiRequest('/api/export');
        const configData = result.data;

        // 创建下载链接
        const dataStr = JSON.stringify(configData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `browser-instances-config-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        showToast('配置导出成功！', 'success');

    } catch (error) {
        showToast(`导出失败: ${error.message}`, 'error');
    }
}

// 显示导入模态框
function showImportModal() {
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();
}

// 显示帮助模态框
function showHelpModal() {
    const modal = new bootstrap.Modal(document.getElementById('helpModal'));
    modal.show();
}

// 导入配置
async function importConfig() {
    const button = event.target;
    showLoading(button);

    try {
        const fileInput = document.getElementById('importFile');
        const file = fileInput.files[0];

        if (!file) {
            showToast('请选择配置文件', 'warning');
            return;
        }

        const fileContent = await file.text();
        const configData = JSON.parse(fileContent);

        const result = await apiRequest('/api/import', {
            method: 'POST',
            body: JSON.stringify({ config: configData })
        });

        showToast(result.message, 'success');
        bootstrap.Modal.getInstance(document.getElementById('importModal')).hide();

        // 重置表单
        fileInput.value = '';
        document.getElementById('importPreview').style.display = 'none';

        // 刷新页面
        setTimeout(() => location.reload(), 1000);

    } catch (error) {
        if (error instanceof SyntaxError) {
            showToast('配置文件格式错误，请检查JSON格式', 'error');
        } else {
            showToast(`导入失败: ${error.message}`, 'error');
        }
    } finally {
        hideLoading(button);
    }
}

// 查看实例详情
async function viewInstanceDetails(name) {
    try {
        const result = await apiRequest(`/api/instances/${name}`);
        const instance = result.data;

        const detailsHtml = `
            <div class="instance-details">
                <h5><i class="fab fa-chrome me-2"></i>${instance.display_name || instance.name}</h5>
                <table class="table table-sm">
                    <tr><td><strong>实例名称:</strong></td><td>${instance.name}</td></tr>
                    <tr><td><strong>显示名称:</strong></td><td>${instance.display_name || '未设置'}</td></tr>
                    <tr><td><strong>描述:</strong></td><td>${instance.description || '无描述'}</td></tr>
                    <tr><td><strong>主页:</strong></td><td><a href="${instance.homepage}" target="_blank">${instance.homepage}</a></td></tr>
                    <tr><td><strong>分组:</strong></td><td>${instance.group || 'default'}</td></tr>
                    <tr><td><strong>状态:</strong></td><td><span class="badge ${instance.status === 'running' ? 'bg-success' : 'bg-secondary'}">${instance.status === 'running' ? '运行中' : '已停止'}</span></td></tr>
                    <tr><td><strong>创建日期:</strong></td><td>${instance.created_date || '未知'}</td></tr>
                    <tr><td><strong>最后更新:</strong></td><td>${instance.last_updated || '未知'}</td></tr>
                </table>
            </div>
        `;

        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>实例详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">${detailsHtml}</div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // 模态框关闭后移除元素
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

    } catch (error) {
        showToast(`获取实例详情失败: ${error.message}`, 'error');
    }
}

// 复制实例
async function duplicateInstance(name) {
    try {
        const result = await apiRequest(`/api/instances/${name}`);
        const instance = result.data;

        // 生成新的实例名称
        const newName = `${name}_copy_${Date.now()}`;
        const newDisplayName = `${instance.display_name || instance.name} - 副本`;

        const duplicateData = {
            name: newName,
            display_name: newDisplayName,
            description: `${instance.description || ''} (复制自 ${name})`,
            homepage: instance.homepage,
            group: instance.group || 'default'
        };

        const createResult = await apiRequest('/api/instances', {
            method: 'POST',
            body: JSON.stringify(duplicateData)
        });

        showToast(`实例复制成功: ${newDisplayName}`, 'success');
        setTimeout(() => location.reload(), 1000);

    } catch (error) {
        showToast(`复制实例失败: ${error.message}`, 'error');
    }
}

// 打开实例目录
async function openInstanceFolder(name) {
    try {
        // 这个功能需要后端支持，暂时显示路径信息
        showToast(`实例目录: instances/${name}/`, 'info');

        // 如果需要实际打开文件夹，需要后端API支持
        // const result = await apiRequest(`/api/instances/${name}/open-folder`, { method: 'POST' });

    } catch (error) {
        showToast(`打开目录失败: ${error.message}`, 'error');
    }
}

// 显示实例高级设置
async function showInstanceSettings(name) {
    try {
        const result = await apiRequest(`/api/instances/${name}`);
        const instance = result.data;

        const settingsHtml = `
            <div class="instance-settings">
                <form id="settingsForm">
                    <div class="mb-3">
                        <label class="form-label"><strong>Chrome启动参数</strong></label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="incognito" ${instance.chrome_args?.includes('--incognito') ? 'checked' : ''}>
                            <label class="form-check-label" for="incognito">隐私模式 (--incognito)</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="disableWebSecurity" ${instance.chrome_args?.includes('--disable-web-security') ? 'checked' : ''}>
                            <label class="form-check-label" for="disableWebSecurity">禁用Web安全 (--disable-web-security)</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="disableExtensions" ${instance.chrome_args?.includes('--disable-extensions') ? 'checked' : ''}>
                            <label class="form-check-label" for="disableExtensions">禁用扩展 (--disable-extensions)</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="noSandbox" ${instance.chrome_args?.includes('--no-sandbox') ? 'checked' : ''}>
                            <label class="form-check-label" for="noSandbox">禁用沙盒 (--no-sandbox)</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="customArgs" class="form-label">自定义参数</label>
                        <textarea class="form-control" id="customArgs" rows="3" placeholder="每行一个参数，例如：--window-size=1920,1080">${instance.custom_args?.join('\\n') || ''}</textarea>
                        <div class="form-text">高级用户可以添加自定义Chrome启动参数</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"><strong>性能设置</strong></label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="disableGpu" ${instance.chrome_args?.includes('--disable-gpu') ? 'checked' : ''}>
                            <label class="form-check-label" for="disableGpu">禁用GPU加速</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="lowMemory" ${instance.chrome_args?.includes('--memory-pressure-off') ? 'checked' : ''}>
                            <label class="form-check-label" for="lowMemory">低内存模式</label>
                        </div>
                    </div>
                </form>
            </div>
        `;

        // 创建设置模态框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-cog me-2"></i>高级设置 - ${instance.display_name || instance.name}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">${settingsHtml}</div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveInstanceSettings('${name}')">保存设置</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // 模态框关闭后移除元素
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

    } catch (error) {
        showToast(`获取实例设置失败: ${error.message}`, 'error');
    }
}

// 保存实例设置
async function saveInstanceSettings(name) {
    try {
        const chromeArgs = [];
        const customArgs = [];

        // 收集选中的Chrome参数
        if (document.getElementById('incognito').checked) chromeArgs.push('--incognito');
        if (document.getElementById('disableWebSecurity').checked) chromeArgs.push('--disable-web-security');
        if (document.getElementById('disableExtensions').checked) chromeArgs.push('--disable-extensions');
        if (document.getElementById('noSandbox').checked) chromeArgs.push('--no-sandbox');
        if (document.getElementById('disableGpu').checked) chromeArgs.push('--disable-gpu');
        if (document.getElementById('lowMemory').checked) chromeArgs.push('--memory-pressure-off');

        // 收集自定义参数
        const customArgsText = document.getElementById('customArgs').value.trim();
        if (customArgsText) {
            customArgs.push(...customArgsText.split('\\n').filter(arg => arg.trim()));
        }

        // 合并所有参数
        const allArgs = [...chromeArgs, ...customArgs];

        const updateData = {
            chrome_args: allArgs,
            custom_args: customArgs
        };

        const result = await apiRequest(`/api/instances/${name}`, {
            method: 'PUT',
            body: JSON.stringify(updateData)
        });

        showToast('设置保存成功！', 'success');

        // 关闭模态框
        const modal = document.querySelector('.modal.show');
        if (modal) {
            bootstrap.Modal.getInstance(modal).hide();
        }

    } catch (error) {
        showToast(`保存设置失败: ${error.message}`, 'error');
    }
}

// 快速启动所有实例
async function quickLaunchAll() {
    try {
        const instanceCards = document.querySelectorAll('.instance-card');
        const instanceNames = Array.from(instanceCards).map(card => card.dataset.instance);

        if (instanceNames.length === 0) {
            showToast('没有可启动的实例', 'warning');
            return;
        }

        if (!confirm(`确定要启动所有 ${instanceNames.length} 个实例吗？`)) {
            return;
        }

        showToast('正在启动所有实例...', 'info');

        const result = await apiRequest('/api/batch/launch', {
            method: 'POST',
            body: JSON.stringify({ instances: instanceNames })
        });

        showToast(result.message, 'success');
        setTimeout(() => location.reload(), 2000);

    } catch (error) {
        showToast(`批量启动失败: ${error.message}`, 'error');
    }
}

// 高级搜索功能
function advancedSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const groupFilter = document.getElementById('groupFilter').value;

    const instanceCards = document.querySelectorAll('.instance-card');
    let visibleCount = 0;
    let matchedInstances = [];

    instanceCards.forEach(card => {
        const instanceName = card.querySelector('.instance-title').textContent.toLowerCase();
        const instanceDescription = card.querySelector('.instance-description')?.textContent.toLowerCase() || '';
        const instanceStatus = card.querySelector('.status-badge').classList.contains('status-running') ? 'running' : 'stopped';

        // 获取分组信息
        const groupElement = card.querySelector('.meta-item:nth-child(2)');
        const instanceGroup = groupElement ? groupElement.textContent.trim() : 'default';

        // 高级搜索匹配
        const matchesSearch = !searchTerm ||
            instanceName.includes(searchTerm) ||
            instanceDescription.includes(searchTerm) ||
            instanceGroup.toLowerCase().includes(searchTerm);

        const matchesStatus = !statusFilter || instanceStatus === statusFilter;
        const matchesGroup = !groupFilter || instanceGroup === groupFilter;

        if (matchesSearch && matchesStatus && matchesGroup) {
            card.style.display = 'block';
            visibleCount++;
            matchedInstances.push({
                name: card.dataset.instance,
                displayName: card.querySelector('.instance-title').textContent.trim(),
                group: instanceGroup,
                status: instanceStatus
            });
        } else {
            card.style.display = 'none';
        }
    });

    // 更新搜索结果统计
    updateSearchResults(visibleCount, matchedInstances);
}

// 更新搜索结果统计
function updateSearchResults(count, instances) {
    const instancesList = document.getElementById('instancesList');

    // 移除之前的搜索结果提示
    const existingResult = instancesList.querySelector('.search-result-info');
    if (existingResult) {
        existingResult.remove();
    }

    if (count === 0) {
        const noResults = document.createElement('div');
        noResults.className = 'empty-state search-result-info';
        noResults.innerHTML = `
            <i class="fas fa-search"></i>
            <h4>未找到匹配的实例</h4>
            <p>尝试调整搜索条件或过滤器</p>
            <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                <i class="fas fa-times me-2"></i>清除过滤器
            </button>
        `;
        instancesList.appendChild(noResults);
    } else if (document.getElementById('searchInput').value ||
               document.getElementById('statusFilter').value ||
               document.getElementById('groupFilter').value) {
        // 显示搜索结果统计
        const resultInfo = document.createElement('div');
        resultInfo.className = 'alert alert-info search-result-info';
        resultInfo.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>
            找到 ${count} 个匹配的实例
            ${instances.length > 0 ? `<small class="d-block mt-1">包含: ${instances.slice(0, 3).map(i => i.displayName).join(', ')}${instances.length > 3 ? '...' : ''}</small>` : ''}
        `;
        instancesList.insertBefore(resultInfo, instancesList.firstChild);
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 检查Chrome状态
    checkHealth();

    // 更新统计
    updateStats();

    // 定期更新实例状态
    setInterval(updateInstancesStatus, 5000);

    // 添加搜索框快捷键支持
    document.getElementById('searchInput').addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            this.value = '';
            filterInstances();
        }
    });

    // 添加全局键盘快捷键支持
    document.addEventListener('keydown', function(e) {
        // Ctrl+N: 新建实例
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            showCreateModal();
        }

        // Ctrl+R: 刷新列表
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            refreshInstances();
        }

        // Ctrl+B: 批量操作
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            toggleBatchMode();
        }

        // Ctrl+E: 导出配置
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportConfig();
        }

        // Ctrl+I: 导入配置
        if (e.ctrlKey && e.key === 'i') {
            e.preventDefault();
            showImportModal();
        }

        // F5: 刷新页面
        if (e.key === 'F5') {
            e.preventDefault();
            location.reload();
        }

        // /: 聚焦搜索框
        if (e.key === '/' && !e.ctrlKey && !e.altKey) {
            e.preventDefault();
            document.getElementById('searchInput').focus();
        }
    });

    // 添加复选框事件监听
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('instance-checkbox')) {
            if (e.target.checked) {
                selectedInstances.add(e.target.value);
            } else {
                selectedInstances.delete(e.target.value);
            }
            updateSelectedCount();
        }

        // 文件选择预览
        if (e.target.id === 'importFile') {
            previewImportFile(e.target);
        }
    });
});

// 预览导入文件
async function previewImportFile(fileInput) {
    const file = fileInput.files[0];
    const previewDiv = document.getElementById('importPreview');
    const previewContent = document.getElementById('previewContent');

    if (!file) {
        previewDiv.style.display = 'none';
        return;
    }

    try {
        const fileContent = await file.text();
        const configData = JSON.parse(fileContent);

        if (!configData.instances || !Array.isArray(configData.instances)) {
            throw new Error('无效的配置文件格式');
        }

        // 生成预览内容
        let previewHtml = `
            <div class="mb-2">
                <strong>配置版本:</strong> ${configData.version || '未知'}
            </div>
            <div class="mb-2">
                <strong>导出日期:</strong> ${configData.export_date || '未知'}
            </div>
            <div class="mb-2">
                <strong>实例数量:</strong> ${configData.instances.length}
            </div>
            <div class="mb-2">
                <strong>实例列表:</strong>
            </div>
            <ul class="list-unstyled ms-3">
        `;

        configData.instances.forEach(instance => {
            previewHtml += `
                <li class="mb-1">
                    <i class="fab fa-chrome me-2"></i>
                    <strong>${instance.display_name || instance.name}</strong>
                    ${instance.description ? `<small class="text-muted"> - ${instance.description}</small>` : ''}
                </li>
            `;
        });

        previewHtml += '</ul>';
        previewContent.innerHTML = previewHtml;
        previewDiv.style.display = 'block';

    } catch (error) {
        previewContent.innerHTML = `
            <div class="text-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                文件格式错误: ${error.message}
            </div>
        `;
        previewDiv.style.display = 'block';
    }
});
</script>
{% endblock %}
