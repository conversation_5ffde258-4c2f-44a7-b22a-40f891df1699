#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器多账号绿色版 v0.002 - Web统一入口
基于Chrome Portable的多账号隔离浏览器解决方案

Author: AI Architecture System
Date: 2025-01-29
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from flask import Flask, render_template, request, jsonify, redirect, url_for
from werkzeug.serving import run_simple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from core.browser_manager import BrowserManager
    from core.config_manager import ConfigManager
    # from core.icon_manager import IconManager  # 暂时禁用
except ImportError:
    # 如果核心模块不存在，创建基础实现
    print("核心模块不存在，使用基础实现...")

app = Flask(__name__)
app.secret_key = 'browser-multi-account-v0002'

# 全局配置
CONFIG = {
    'chrome_portable_path': 'GoogleChromePortable/GoogleChromePortable.exe',
    'instances_dir': 'instances',
    'icons_dir': 'icons',
    'config_dir': 'config'
}

class SimpleBrowserManager:
    """简化的浏览器管理器实现"""
    
    def __init__(self):
        self.instances_dir = Path(CONFIG['instances_dir'])
        self.chrome_path = Path(CONFIG['chrome_portable_path'])
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        self.instances_dir.mkdir(exist_ok=True)
        Path(CONFIG['icons_dir']).mkdir(exist_ok=True)
        Path(CONFIG['config_dir']).mkdir(exist_ok=True)
    
    def list_instances(self):
        """列出所有浏览器实例"""
        instances = []
        if not self.instances_dir.exists():
            return instances
        
        for instance_dir in self.instances_dir.iterdir():
            if instance_dir.is_dir():
                config_file = instance_dir / 'config.json'
                if config_file.exists():
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        config['name'] = instance_dir.name
                        config['status'] = 'stopped'  # 简化状态检查
                        # 确保所有实例都有分组信息
                        if 'group' not in config:
                            config['group'] = 'default'
                        instances.append(config)
                    except Exception as e:
                        print(f"读取实例配置失败 {instance_dir.name}: {e}")
        
        return instances
    
    def create_instance(self, name, display_name, description="", homepage="https://www.google.com", group="default"):
        """创建新的浏览器实例"""
        instance_dir = self.instances_dir / name
        if instance_dir.exists():
            raise ValueError(f"实例 {name} 已存在")

        # 创建实例目录结构
        instance_dir.mkdir(parents=True)
        data_dir = instance_dir / 'Data' / 'profile'
        data_dir.mkdir(parents=True)

        # 创建配置文件
        config = {
            'name': name,
            'display_name': display_name,
            'description': description,
            'homepage': homepage,
            'icon': 'default',
            'group': group,
            'created_date': '2025-01-29',
            'last_updated': '2025-01-29',
            'chrome_args': [
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-background-timer-throttling'
            ]
        }

        config_file = instance_dir / 'config.json'
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        return config
    
    def launch_instance(self, name):
        """启动浏览器实例"""
        if not self.chrome_path.exists():
            raise FileNotFoundError("Chrome Portable 未找到")
        
        instance_dir = self.instances_dir / name
        if not instance_dir.exists():
            raise ValueError(f"实例 {name} 不存在")
        
        # 读取配置
        config_file = instance_dir / 'config.json'
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 构建启动命令
        data_dir = instance_dir / 'Data' / 'profile'
        cmd = [
            str(self.chrome_path),
            f'--user-data-dir={data_dir.absolute()}',
            f'--app-name={config["display_name"]}',
            config['homepage']
        ] + config.get('chrome_args', [])
        
        # 启动浏览器
        subprocess.Popen(cmd, cwd=str(self.chrome_path.parent))
        return True
    
    def get_instance(self, name):
        """获取指定实例"""
        instance_dir = self.instances_dir / name
        config_file = instance_dir / 'config.json'

        if not config_file.exists():
            return None

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            config['name'] = name
            config['status'] = self._get_instance_status(name)
            return config
        except Exception as e:
            print(f"读取实例配置失败 {name}: {e}")
            return None

    def _get_instance_status(self, name):
        """获取实例运行状态"""
        # 简化的状态检查，实际项目中可以通过进程检查
        return 'stopped'  # 默认返回停止状态

    def update_instance(self, name, **kwargs):
        """更新实例配置"""
        instance_dir = self.instances_dir / name
        config_file = instance_dir / 'config.json'

        if not config_file.exists():
            raise ValueError(f"实例 {name} 不存在")

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 更新配置
            config.update(kwargs)
            config['last_updated'] = '2025-01-29'

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            return config
        except Exception as e:
            raise ValueError(f"更新实例配置失败: {e}")

    def delete_instance(self, name):
        """删除浏览器实例"""
        instance_dir = self.instances_dir / name
        if not instance_dir.exists():
            raise ValueError(f"实例 {name} 不存在")

        import shutil
        shutil.rmtree(instance_dir)
        return True

class SimpleConfigManager:
    """简化的配置管理器"""

    def get_batch_templates(self):
        """获取批量配置模板"""
        return {
            'basic_set': {
                'name': '基础实例集合',
                'description': '工作、个人、购物三个基础实例',
                'instances': [
                    {
                        'name': 'work',
                        'display_name': 'Chrome - 工作',
                        'description': '工作专用浏览器',
                        'homepage': 'https://www.google.com',
                        'icon': 'work'
                    },
                    {
                        'name': 'personal',
                        'display_name': 'Chrome - 个人',
                        'description': '个人使用浏览器',
                        'homepage': 'https://www.google.com',
                        'icon': 'personal'
                    },
                    {
                        'name': 'shopping',
                        'display_name': 'Chrome - 购物',
                        'description': '购物专用浏览器',
                        'homepage': 'https://www.taobao.com',
                        'icon': 'shopping'
                    }
                ]
            },
            'developer_set': {
                'name': '开发者实例集合',
                'description': '专为开发者设计的实例集合',
                'instances': [
                    {
                        'name': 'dev_frontend',
                        'display_name': 'Chrome - 前端开发',
                        'description': '前端开发专用',
                        'homepage': 'https://developer.mozilla.org',
                        'icon': 'dev'
                    },
                    {
                        'name': 'dev_backend',
                        'display_name': 'Chrome - 后端开发',
                        'description': '后端开发专用',
                        'homepage': 'https://github.com',
                        'icon': 'dev'
                    },
                    {
                        'name': 'dev_test',
                        'display_name': 'Chrome - 测试环境',
                        'description': '测试环境专用',
                        'homepage': 'http://localhost:3000',
                        'icon': 'dev'
                    }
                ]
            }
        }

# 初始化管理器
browser_manager = SimpleBrowserManager()
config_manager = SimpleConfigManager()
# 暂时禁用图标管理器
icon_manager = None

@app.route('/')
def index():
    """主页"""
    instances = browser_manager.list_instances()
    return render_template('index.html', instances=instances)

@app.route('/settings')
def settings():
    """设置页面"""
    return render_template('settings.html')

@app.route('/api/instances')
def api_instances():
    """获取所有实例"""
    try:
        instances = browser_manager.list_instances()
        return jsonify({'success': True, 'data': instances})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/instances', methods=['POST'])
def api_create_instance():
    """创建新实例"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        display_name = data.get('display_name', '').strip()
        description = data.get('description', '').strip()
        homepage = data.get('homepage', 'https://www.google.com').strip()
        group = data.get('group', 'default').strip()

        if not name or not display_name:
            return jsonify({'success': False, 'error': '实例名称和显示名称不能为空'})

        # 验证名称格式
        if not name.replace('_', '').replace('-', '').isalnum():
            return jsonify({'success': False, 'error': '实例名称只能包含字母、数字、下划线和连字符'})

        config = browser_manager.create_instance(name, display_name, description, homepage, group)
        return jsonify({'success': True, 'data': config})
    
    except ValueError as e:
        return jsonify({'success': False, 'error': str(e)})
    except Exception as e:
        return jsonify({'success': False, 'error': f'创建实例失败: {str(e)}'})

@app.route('/api/instances/<name>/launch', methods=['POST'])
def api_launch_instance(name):
    """启动实例"""
    try:
        browser_manager.launch_instance(name)
        return jsonify({'success': True, 'message': f'实例 {name} 启动成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': f'启动失败: {str(e)}'})

@app.route('/api/instances/<name>/stop', methods=['POST'])
def api_stop_instance(name):
    """停止实例"""
    try:
        # 简化的停止实现，实际项目中需要进程管理
        return jsonify({'success': True, 'message': f'实例 {name} 停止成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': f'停止失败: {str(e)}'})

@app.route('/api/instances/<name>', methods=['GET'])
def api_get_instance(name):
    """获取单个实例详情"""
    try:
        instance = browser_manager.get_instance(name)
        if not instance:
            return jsonify({'success': False, 'error': f'实例 {name} 不存在'})
        return jsonify({'success': True, 'data': instance})
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取实例失败: {str(e)}'})

@app.route('/api/instances/<name>', methods=['PUT'])
def api_update_instance(name):
    """更新实例配置"""
    try:
        data = request.get_json()
        instance = browser_manager.get_instance(name)
        if not instance:
            return jsonify({'success': False, 'error': f'实例 {name} 不存在'})

        # 更新配置
        update_data = {}
        if 'display_name' in data:
            update_data['display_name'] = data['display_name'].strip()
        if 'description' in data:
            update_data['description'] = data['description'].strip()
        if 'homepage' in data:
            update_data['homepage'] = data['homepage'].strip()
        if 'icon' in data:
            update_data['icon'] = data['icon'].strip()
        if 'chrome_args' in data:
            update_data['chrome_args'] = data['chrome_args']

        updated_instance = browser_manager.update_instance(name, **update_data)
        return jsonify({'success': True, 'data': updated_instance, 'message': f'实例 {name} 更新成功'})

    except Exception as e:
        return jsonify({'success': False, 'error': f'更新失败: {str(e)}'})

@app.route('/api/instances/<name>', methods=['DELETE'])
def api_delete_instance(name):
    """删除实例"""
    try:
        browser_manager.delete_instance(name)
        return jsonify({'success': True, 'message': f'实例 {name} 删除成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': f'删除失败: {str(e)}'})

@app.route('/api/instances/<name>/status')
def api_get_instance_status(name):
    """获取实例状态"""
    try:
        instance = browser_manager.get_instance(name)
        if not instance:
            return jsonify({'success': False, 'error': f'实例 {name} 不存在'})

        status = browser_manager._get_instance_status(name)
        return jsonify({'success': True, 'data': {'name': name, 'status': status}})
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取状态失败: {str(e)}'})

@app.route('/api/instances/status/all')
def api_get_all_instances_status():
    """获取所有实例状态"""
    try:
        instances = browser_manager.list_instances()
        status_list = []
        for instance in instances:
            status_list.append({
                'name': instance['name'],
                'status': instance['status']
            })
        return jsonify({'success': True, 'data': status_list})
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取状态失败: {str(e)}'})

@app.route('/api/batch/templates')
def api_get_batch_templates():
    """获取批量配置模板"""
    try:
        templates = config_manager.get_batch_templates()
        return jsonify({'success': True, 'data': templates})
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取模板失败: {str(e)}'})

@app.route('/api/batch/create', methods=['POST'])
def api_batch_create():
    """批量创建实例"""
    try:
        data = request.get_json()
        template_name = data.get('template')
        custom_config = data.get('config')

        created_instances = []

        if template_name:
            # 使用预定义模板
            templates = config_manager.get_batch_templates()

            if template_name not in templates:
                return jsonify({'success': False, 'error': f'模板 {template_name} 不存在'})

            template = templates[template_name]
            instances_config = template.get('instances', [])

        elif custom_config:
            # 使用自定义配置
            prefix = custom_config.get('prefix', 'instance')
            count = int(custom_config.get('count', 3))

            instances_config = []
            for i in range(1, count + 1):
                instances_config.append({
                    'name': f'{prefix}_{i}',
                    'display_name': f'Chrome - {prefix} {i}',
                    'description': f'批量创建的实例 {i}',
                    'homepage': 'https://www.google.com',
                    'icon': 'default'
                })
        else:
            return jsonify({'success': False, 'error': '请提供模板名称或自定义配置'})

        # 创建实例
        for instance_config in instances_config:
            try:
                # 检查实例是否已存在
                if browser_manager.get_instance(instance_config['name']):
                    continue  # 跳过已存在的实例

                instance = browser_manager.create_instance(
                    name=instance_config['name'],
                    display_name=instance_config['display_name'],
                    description=instance_config.get('description', ''),
                    homepage=instance_config.get('homepage', 'https://www.google.com')
                )
                created_instances.append(instance)

            except Exception as e:
                print(f"创建实例 {instance_config['name']} 失败: {e}")
                continue

        return jsonify({
            'success': True,
            'data': created_instances,
            'message': f'成功创建 {len(created_instances)} 个实例'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'批量创建失败: {str(e)}'})

@app.route('/api/batch/launch', methods=['POST'])
def api_batch_launch():
    """批量启动实例"""
    try:
        data = request.get_json()
        instance_names = data.get('instances', [])

        if not instance_names:
            return jsonify({'success': False, 'error': '请选择要启动的实例'})

        results = []
        success_count = 0

        for name in instance_names:
            try:
                browser_manager.launch_instance(name)
                results.append({'name': name, 'status': 'success', 'message': '启动成功'})
                success_count += 1
            except Exception as e:
                results.append({'name': name, 'status': 'error', 'message': str(e)})

        return jsonify({
            'success': True,
            'data': results,
            'message': f'成功启动 {success_count}/{len(instance_names)} 个实例'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'批量启动失败: {str(e)}'})

@app.route('/api/batch/delete', methods=['POST'])
def api_batch_delete():
    """批量删除实例"""
    try:
        data = request.get_json()
        instance_names = data.get('instances', [])

        if not instance_names:
            return jsonify({'success': False, 'error': '请选择要删除的实例'})

        results = []
        success_count = 0

        for name in instance_names:
            try:
                browser_manager.delete_instance(name)
                results.append({'name': name, 'status': 'success', 'message': '删除成功'})
                success_count += 1
            except Exception as e:
                results.append({'name': name, 'status': 'error', 'message': str(e)})

        return jsonify({
            'success': True,
            'data': results,
            'message': f'成功删除 {success_count}/{len(instance_names)} 个实例'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'批量删除失败: {str(e)}'})

@app.route('/api/groups')
def api_get_groups():
    """获取所有分组"""
    try:
        instances = browser_manager.list_instances()
        groups = {}

        for instance in instances:
            group = instance.get('group', 'default')
            if group not in groups:
                groups[group] = []
            groups[group].append(instance)

        return jsonify({'success': True, 'data': groups})
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取分组失败: {str(e)}'})

@app.route('/api/groups/<group_name>')
def api_get_group_instances(group_name):
    """获取指定分组的实例"""
    try:
        instances = browser_manager.list_instances()
        group_instances = [inst for inst in instances if inst.get('group', 'default') == group_name]

        return jsonify({'success': True, 'data': group_instances})
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取分组实例失败: {str(e)}'})

@app.route('/api/export')
def api_export_config():
    """导出所有实例配置"""
    try:
        instances = browser_manager.list_instances()
        export_data = {
            'version': '0.002',
            'export_date': '2025-01-29',
            'instances': instances
        }

        return jsonify({'success': True, 'data': export_data})
    except Exception as e:
        return jsonify({'success': False, 'error': f'导出配置失败: {str(e)}'})

@app.route('/api/import', methods=['POST'])
def api_import_config():
    """导入实例配置"""
    try:
        data = request.get_json()
        import_data = data.get('config', {})

        if not import_data or 'instances' not in import_data:
            return jsonify({'success': False, 'error': '无效的配置数据'})

        instances_config = import_data['instances']
        created_instances = []
        skipped_instances = []

        for instance_config in instances_config:
            try:
                name = instance_config.get('name')
                if not name:
                    continue

                # 检查实例是否已存在
                if browser_manager.get_instance(name):
                    skipped_instances.append(name)
                    continue

                instance = browser_manager.create_instance(
                    name=name,
                    display_name=instance_config.get('display_name', name),
                    description=instance_config.get('description', ''),
                    homepage=instance_config.get('homepage', 'https://www.google.com'),
                    group=instance_config.get('group', 'default')
                )
                created_instances.append(instance)

            except Exception as e:
                print(f"导入实例 {instance_config.get('name', 'unknown')} 失败: {e}")
                continue

        return jsonify({
            'success': True,
            'data': {
                'created': created_instances,
                'skipped': skipped_instances
            },
            'message': f'成功导入 {len(created_instances)} 个实例，跳过 {len(skipped_instances)} 个已存在的实例'
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'导入配置失败: {str(e)}'})

@app.route('/api/icons')
def api_list_icons():
    """获取图标列表"""
    try:
        if not icon_manager:
            return jsonify({'success': False, 'error': '图标管理器未初始化'})

        icons = icon_manager.list_icons()
        return jsonify({'success': True, 'data': icons})
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取图标列表失败: {str(e)}'})

@app.route('/api/icons/<icon_id>')
def api_get_icon(icon_id):
    """获取指定图标信息"""
    try:
        if not icon_manager:
            return jsonify({'success': False, 'error': '图标管理器未初始化'})

        icon = icon_manager.get_icon(icon_id)
        if not icon:
            return jsonify({'success': False, 'error': f'图标 {icon_id} 不存在'})

        return jsonify({'success': True, 'data': icon})
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取图标失败: {str(e)}'})

@app.route('/api/icons/<icon_id>', methods=['DELETE'])
def api_delete_icon(icon_id):
    """删除图标"""
    try:
        if not icon_manager:
            return jsonify({'success': False, 'error': '图标管理器未初始化'})

        icon_manager.delete_icon(icon_id)
        return jsonify({'success': True, 'message': f'图标 {icon_id} 删除成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': f'删除图标失败: {str(e)}'})

@app.route('/api/icons/categories')
def api_get_icon_categories():
    """获取图标分类"""
    try:
        if not icon_manager:
            return jsonify({'success': False, 'error': '图标管理器未初始化'})

        categories = icon_manager.get_icon_categories()
        return jsonify({'success': True, 'data': categories})
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取图标分类失败: {str(e)}'})

@app.route('/health')
def health_check():
    """健康检查"""
    chrome_exists = Path(CONFIG['chrome_portable_path']).exists()
    instances = browser_manager.list_instances()

    # 统计分组信息
    groups = {}
    for instance in instances:
        group = instance.get('group', 'default')
        groups[group] = groups.get(group, 0) + 1

    return jsonify({
        'status': 'healthy',
        'chrome_portable': chrome_exists,
        'instances_count': len(instances),
        'groups': groups,
        'system_info': {
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'project_version': 'v0.002',
            'chrome_path': str(Path(CONFIG['chrome_portable_path']).absolute()),
            'instances_dir': str(Path(CONFIG['instances_dir']).absolute())
        }
    })

@app.route('/api/statistics')
def api_get_statistics():
    """获取详细统计信息"""
    try:
        instances = browser_manager.list_instances()

        # 基础统计
        total_count = len(instances)
        running_count = sum(1 for inst in instances if inst.get('status') == 'running')
        stopped_count = total_count - running_count

        # 分组统计
        groups = {}
        for instance in instances:
            group = instance.get('group', 'default')
            if group not in groups:
                groups[group] = {'total': 0, 'running': 0, 'stopped': 0}
            groups[group]['total'] += 1
            if instance.get('status') == 'running':
                groups[group]['running'] += 1
            else:
                groups[group]['stopped'] += 1

        # 图标统计
        icons = {}
        for instance in instances:
            icon = instance.get('icon', 'default')
            icons[icon] = icons.get(icon, 0) + 1

        return jsonify({
            'success': True,
            'data': {
                'total_instances': total_count,
                'running_instances': running_count,
                'stopped_instances': stopped_count,
                'groups': groups,
                'icons': icons,
                'chrome_available': Path(CONFIG['chrome_portable_path']).exists()
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': f'获取统计信息失败: {str(e)}'})

def check_environment():
    """检查运行环境"""
    issues = []
    
    # 检查Chrome Portable
    if not Path(CONFIG['chrome_portable_path']).exists():
        issues.append("Chrome Portable 未找到，请确保 GoogleChromePortable 目录存在")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        issues.append("需要Python 3.7或更高版本")
    
    return issues

if __name__ == '__main__':
    print("🚀 浏览器多账号绿色版 v0.002 启动中...")
    print("=" * 50)
    
    # 环境检查
    issues = check_environment()
    if issues:
        print("⚠️  环境检查发现问题:")
        for issue in issues:
            print(f"   - {issue}")
        print()
    
    print("📊 系统信息:")
    print(f"   - Python版本: {sys.version}")
    print(f"   - 项目目录: {project_root}")
    print(f"   - Chrome路径: {CONFIG['chrome_portable_path']}")
    print(f"   - 实例目录: {CONFIG['instances_dir']}")
    
    instances = browser_manager.list_instances()
    print(f"   - 现有实例: {len(instances)}个")
    
    print()
    print("🌐 Web界面地址: http://127.0.0.1:5000")
    print("📖 使用说明: 在浏览器中打开上述地址进行管理")
    print("=" * 50)
    
    try:
        # 启动Flask应用
        app.run(host='127.0.0.1', port=5000, debug=True)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
